package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按日巡检报告领域对象转换器
 * 用于领域对象和数据对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface DailyInspectionReportMapper {

    /**
     * 领域对象转DO（用于查询条件）
     *
     * @param report 领域对象
     * @return DO对象
     */
    @IgnoreAuditFields
    DailyInspectionReportDO toDailyInspectionReportDO(DailyInspectionReport report);

    /**
     * DO列表转领域对象列表
     *
     * @param reportDOList DO列表
     * @return 领域对象列表
     */
    List<DailyInspectionReport> toDailyInspectionReportList(List<DailyInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转领域对象
     *
     * @param detailDO 详细内容DO
     * @return 详细内容领域对象
     */
    DailyInspectionReportDetail toDailyInspectionReportDetail(DailyInspectionReportDetailDO detailDO);
}
