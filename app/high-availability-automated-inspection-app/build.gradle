apply plugin: 'org.springframework.boot'

dependencies {
    implementation project(":rest:high-availability-inspection-rest")

    implementation 'com.cmpay:lemon-framework-starter-actuator-prometheus'
    implementation 'com.cmpay:lemon-framework-starter-actuator-security'
    implementation('com.cmpay:cmpay-tracing-starter')

    // Replaced knife4j with Springdoc OpenAPI (already included in rest module)
    // implementation 'com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3'
}

springBoot {
    mainClass = 'com.cmpay.hacp.inspection.HighAvailabilityAutomatedInspectionApplication'
}
