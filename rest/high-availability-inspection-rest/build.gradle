ext {
    versions = [
            mapstruct: '1.6.3',
            springdoc: '1.6.15', // Compatible with Spring Boot 2.6.x and Java 8
    ]
}

dependencies {
    implementation project(":service:high-availability-inspection-service")

    implementation 'com.cmpay:lemon-framework-starter-context'
    implementation 'com.cmpay:lemon-framework-starter-session-hazelcast'
    // Replaced lemon-swagger-starter with Springdoc OpenAPI
    implementation "org.springdoc:springdoc-openapi-ui:${versions.springdoc}"

    implementation "org.mapstruct:mapstruct:${versions.mapstruct}"

    annotationProcessor "org.mapstruct:mapstruct-processor:${versions.mapstruct}"
}