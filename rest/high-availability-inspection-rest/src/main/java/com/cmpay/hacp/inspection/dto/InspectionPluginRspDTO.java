package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import com.cmpay.hacp.inspection.domain.model.enums.ScriptResultType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 巡检插件响应DTO
 */
@Data
@Schema(description = "巡检插件响应DTO")
public class InspectionPluginRspDTO {
    @Schema(description = "插件ID", example = "1001")
    private String pluginId;

    @Schema(description = "插件名称", example = "CPU使用率检查")
    private String name;

    @Schema(description = "插件类型(SHELL、PYTHON等)", example = "SHELL")
    private PluginType type;

    @Schema(description = "插件状态(0禁用，1启用)", example = "1")
    private PluginStatus status;

    @Schema(description = "插件描述", example = "检查服务器CPU使用率是否超过阈值")
    private String description;

    @Schema(description = "脚本内容", example = "#!/bin/bash\necho \"CPU使用率检查\"")
    private String scriptContent;

    @Schema(description = "脚本输出类型：1-结构化，2-纯文本", example = "1")
    private ScriptResultType scriptResultType;

    @Schema(description = "标签列表")
    private List<TagDTO> tags;

    @Schema(description = "输出字段定义列表")
    private List<PluginScriptResultDTO> results;

    @Schema(description = "参数设置列表")
    private List<PluginScriptParameterDTO> parameters;

    private AuditInfoDTO auditInfo;
}
