package com.cmpay.hacp.inspection.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.enums.PluginStatus;
import com.cmpay.hacp.inspection.domain.model.enums.PluginType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 巡检插件查询请求DTO
 */
@Data
@Schema(description = "巡检插件查询请求DTO")
public class InspectionPluginQueryReqDTO {

    @Schema(description = "插件ID", example = "1001")
    private Long pluginId;

    @Schema(description = "插件名称", example = "CPU使用率检查")
    private String name;

    @Schema(description = "插件类型(SHELL、PYTHON等)", example = "SHELL")
    private PluginType type;

    @Schema(description = "插件状态(0禁用，1启用)", example = "1")
    private PluginStatus status;

    private PageDTO<?> page;
}
