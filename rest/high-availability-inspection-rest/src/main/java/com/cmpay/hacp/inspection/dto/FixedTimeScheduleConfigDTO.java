package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 固定时间调度配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("FIXED_TIME")
@Schema(description = "固定时间调度配置DTO")
public class FixedTimeScheduleConfigDTO extends ScheduleConfigDTO {

    @Schema(description = "执行日期", required = true, example = "2023-12-31")
    @NotNull(message = "执行日期不能为空")
    private LocalDate executionDate;

    @Schema(description = "执行时间", required = true, example = "12:00:00")
    @NotNull(message = "执行时间不能为空")
    private LocalTime executionTime;

    public FixedTimeScheduleConfigDTO() {
        setType(ScheduleType.FIXED_TIME);
    }
}
