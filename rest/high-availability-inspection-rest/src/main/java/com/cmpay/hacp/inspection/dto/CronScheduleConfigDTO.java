package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ScheduleType;
import com.cmpay.hacp.inspection.infrastructure.validation.ValidCronExpression;
import com.fasterxml.jackson.annotation.JsonTypeName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * CRON表达式调度配置DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonTypeName("CRON")
@Schema(description = "CRON表达式调度配置DTO")
public class CronScheduleConfigDTO extends ScheduleConfigDTO {

    @Schema(description = "CRON 表达式", required = true, example = "0 0 12 * * ?")
    @NotBlank(message = "CRON表达式不能为空")
    @ValidCronExpression
    private String cronExpression;

    public CronScheduleConfigDTO() {
        setType(ScheduleType.CRON_EXPRESSION);
    }
}
